import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { ArrowLeft, MessageCircle, Clock, CheckCircle, XCircle, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { formatCurrency } from '@/lib/utils';
import AppLayout from '@/layouts/app-layout';

interface Inquiry {
  id: number;
  message: string;
  contact_preference: 'email' | 'phone' | 'whatsapp';
  status: 'pending' | 'responded' | 'closed';
  created_at: string;
  kost: {
    id: number;
    name: string;
    city: string;
    province: string;
    price_monthly: number;
    images: Array<{
      id: number;
      image_path: string;
      image_type: string;
    }>;
  };
}

interface InquiriesProps {
  inquiries: {
    data: Inquiry[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  auth: {
    user: any;
  };
}

export default function Inquiries({ inquiries, auth }: InquiriesProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'responded': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'responded': return <CheckCircle className="h-4 w-4" />;
      case 'closed': return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'Menunggu Respon';
      case 'responded': return 'Sudah Direspon';
      case 'closed': return 'Ditutup';
      default: return 'Tidak Diketahui';
    }
  };

  const getContactPreferenceText = (preference: string) => {
    switch (preference) {
      case 'email': return 'Email';
      case 'phone': return 'Telepon';
      case 'whatsapp': return 'WhatsApp';
      default: return 'Email';
    }
  };

  return (
    <AppLayout>
      <Head title="Riwayat Inquiry" />
      
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <Link
                href={route('pencari.dashboard')}
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                Kembali ke Dashboard
              </Link>
            </div>
            
            <div className="flex items-center gap-3 mb-2">
              <MessageCircle className="h-8 w-8 text-blue-600" />
              <h1 className="text-3xl font-bold text-gray-900">Riwayat Inquiry</h1>
            </div>
            <p className="text-gray-600">
              Lihat semua inquiry yang pernah Anda kirim dan status responnya
            </p>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Inquiry</p>
                    <p className="text-2xl font-bold text-gray-900">{inquiries.total}</p>
                  </div>
                  <MessageCircle className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Menunggu Respon</p>
                    <p className="text-2xl font-bold text-yellow-600">
                      {inquiries.data.filter(i => i.status === 'pending').length}
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Sudah Direspon</p>
                    <p className="text-2xl font-bold text-green-600">
                      {inquiries.data.filter(i => i.status === 'responded').length}
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Ditutup</p>
                    <p className="text-2xl font-bold text-gray-600">
                      {inquiries.data.filter(i => i.status === 'closed').length}
                    </p>
                  </div>
                  <XCircle className="h-8 w-8 text-gray-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Inquiries List */}
          <div className="space-y-6">
            {inquiries.data.length > 0 ? (
              inquiries.data.map((inquiry) => (
                <Card key={inquiry.id} className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex flex-col lg:flex-row gap-6">
                      {/* Kost Image */}
                      <div className="flex-shrink-0">
                        <div className="w-full lg:w-48 h-32 bg-gray-200 rounded-lg overflow-hidden">
                          {inquiry.kost.images?.[0] ? (
                            <img
                              src={`/storage/${inquiry.kost.images[0].image_path}`}
                              alt={inquiry.kost.name}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-gray-400">
                              <MessageCircle className="h-8 w-8" />
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Inquiry Details */}
                      <div className="flex-1">
                        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 mb-4">
                          <div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-1">
                              {inquiry.kost.name}
                            </h3>
                            <p className="text-gray-600 mb-2">
                              {inquiry.kost.city}, {inquiry.kost.province}
                            </p>
                            <p className="text-lg font-semibold text-blue-600">
                              {formatCurrency(inquiry.kost.price_monthly)} / bulan
                            </p>
                          </div>
                          
                          <div className="flex flex-col items-start sm:items-end gap-2">
                            <Badge className={`${getStatusColor(inquiry.status)} flex items-center gap-1`}>
                              {getStatusIcon(inquiry.status)}
                              {getStatusText(inquiry.status)}
                            </Badge>
                            <p className="text-sm text-gray-500">
                              {new Date(inquiry.created_at).toLocaleDateString('id-ID', {
                                day: 'numeric',
                                month: 'long',
                                year: 'numeric'
                              })}
                            </p>
                          </div>
                        </div>

                        {/* Message */}
                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-700 mb-2">Pesan Anda:</h4>
                          <p className="text-gray-600 bg-gray-50 p-3 rounded-lg">
                            {inquiry.message}
                          </p>
                        </div>

                        {/* Contact Preference */}
                        <div className="mb-4">
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Cara kontak yang diinginkan:</span>{' '}
                            {getContactPreferenceText(inquiry.contact_preference)}
                          </p>
                        </div>

                        {/* Actions */}
                        <div className="flex flex-wrap gap-3">
                          <Button
                            variant="outline"
                            size="sm"
                            asChild
                          >
                            <Link href={route('pencari.kost.show', inquiry.kost.id)}>
                              <Eye className="h-4 w-4 mr-2" />
                              Lihat Kost
                            </Link>
                          </Button>
                          
                          {inquiry.status === 'pending' && (
                            <Button
                              variant="outline"
                              size="sm"
                              asChild
                            >
                              <Link href={route('pencari.kost.show', inquiry.kost.id)} data={{ showInquiry: true }}>
                                <MessageCircle className="h-4 w-4 mr-2" />
                                Kirim Inquiry Lagi
                              </Link>
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="p-12 text-center">
                  <MessageCircle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Belum Ada Inquiry
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Anda belum pernah mengirim inquiry untuk kost manapun.
                  </p>
                  <Button asChild>
                    <Link href={route('pencari.search')}>
                      Cari Kost Sekarang
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Pagination */}
          {inquiries.last_page > 1 && (
            <div className="mt-8 flex justify-center">
              <div className="flex items-center gap-2">
                {Array.from({ length: inquiries.last_page }, (_, i) => i + 1).map((page) => (
                  <Link
                    key={page}
                    href={route('pencari.inquiries', { page })}
                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      page === inquiries.current_page
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                    }`}
                  >
                    {page}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </AppLayout>
  );
}
